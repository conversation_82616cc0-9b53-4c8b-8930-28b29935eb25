<template>
	<view class="height-100vh box-border bg-light page-wrap flex flex-column overflow-y-hidden" :style="{ transform: `translateY(-${pageMove}px)` }">
		<view class="flex-1" id="scrollViewWrap">
			<scroll-view
				ref="scrollView"
				scroll-y="true"
				class="list-wrap box-border no-scrollbar"
				:style="{
					height: scrollViewHeight + 'px'
				}"
				:scroll-top="scrollTop"
				:scroll-with-animation="true"
				:show-scrollbar="false"
				:scroll-anchoring="true"
			>
				<view class="px-4">
					<!-- 名片 -->
					<view class="card-wrap mb-4">
						<view class="card-main position-relative rounded-12 p-base box-border bg-light-detail flex align-center">
							<view class="logo-box">
								<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
							</view>
							<view class="flex-1 text-theme-brand pt-2">
								<view class="font-weight-bold font-14 mb-1">您好，我是陈功</view>
								<view class="font-12">很高兴为您服务，有什么问题尽管问</view>
							</view>
							<view class="flex-shrink flex justify-end">
								<button open-type="share" class="bg-theme-brand text-white py-1 font-12 flex align-center justify-evenly rounded-8 card-box">
									<view class="share-box flex algin-center justify-center">
										<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
									</view>
									名片
								</button>
							</view>
						</view>
					</view>
					<!--列表 -->
					<view class="flex flex-row flex-wrap pb-base">
						<template v-for="(item, inex) in chatList">
							<view v-if="item.user == 'my'" class="flex justify-end width-100">
								<view class="mb-4 bg-theme-brand text-white font-14 rounded-8 rounded-top-right-0 d-inline-block p-base" @longpress="onLongpress">
									{{ item.content }}
									<!-- <zero-markdown-view :markdown="item.content"></zero-markdown-view> -->
								</view>
							</view>
							<view v-else class="flex width-100">
								<view class="mb-base bg-white text-theme-brand font-14 rounded-8 rounded-top-left-0 d-inline-block">
									<zero-markdown-view :markdown="item.content"></zero-markdown-view>
								</view>
							</view>
						</template>
					</view>
					<view id="scrollBottomMarker"></view>
				</view>
			</scroll-view>
			pageMove-{{ pageMove }}
		</view>
		<!-- 底部按钮 -->
		<view class="flex-shrink pageFoot box-border p-4" :style="{marginBottom:systemInfo.safeAreaInsets.bottom+'px'}" >
			<view class="scroll-row mb-4 box-border overflow-x-auto no-scrollbar">
				<view
					v-for="(item, index) in preQuestionList"
					:key="index"
					class="rounded-80 py-1 px-3 text-second bg-info font-12 mr-base mb scroll-row-item"
					@tap="onSelectPrequestion(item)"
				>
					{{ item.content }}
				</view>
			</view>
			<view class="flex align-center justify-evenly mb-base">
				<view class="flex-shrink btn-box rounded-circle bg-white p-1 flex align-center justify-center">
					<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
				</view>
				<view class="flex-1 px-base position-relative">
										<!-- resolved 点击输入框会弹出键盘，但是键盘和输入框纸巾没有间距，不美观，如何处理比较好呢 -->
					<input type="text" v-model="content" placeholder="请输入您的问题" class="input px-3" placeholder-class="text-placeholder font-14" @confirm="onSend" />
				</view>
				<view class="flex-shrink btn-box rounded-circle bg-white p-1 flex align-center justify-center" hover-class="bg-hover" @tap="onSend">
					<image src="/static/images/<EMAIL>" mode="aspectFit" class="width-100 height-100"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
// import zeroMarkdownView from '/uni_modules/zero-markdown-view/components/zero-markdown-view/zero-markdown-view.vue';
import { ref, nextTick, watch,onMounted,onUnmounted, reactive } from 'vue';
import { onShareAppMessage, onLoad, onReady } from '@dcloudio/uni-app';
const PREQUESTION_LIST = [
	{
		content: '了解产品',
		user: 'my'
	},
	{
		content: '性价比最高的产品',
		user: 'my'
	},
	{
		content: '试用场景有哪些？',
		user: 'my'
	}
];
// 变量
const pageMove=ref(0) // 键盘出现往上推移
const scrollView = ref(null);
const scrollTop = ref(0);
const scrollViewHeight = ref(400); // 滚动区域高度
const content = ref(''); // 问题
const preQuestionList = ref(PREQUESTION_LIST); // 预设的问题
const systemInfo=reactive({
	osName:"",
	safeAreaInsets:{}
}) // 系统信息
const chatList = ref([
	{
		id: 1,
		content: '你好！支持以下 Markdown 格式：\n- **加粗文本**\n- *斜体文本*\n- [链接示例](https://example.com)\n- `代码片段`',
		avatar: '/static/avatars/other.png'
	},
	{
		content: '你们有哪些产品',
		user: 'my'
	},
	{
		content: '## md你们有哪些产品',
		user: 'my'
	},
	{
		content: '你好！支持以下 Markdown 格式：\n- **加粗文本**\n- *斜体文本*\n- [链接示例](https://example.com)\n- `代码片段`你好！支持以下 Markdown 格式：\n- **加粗文本**\n- *斜体文本*\n- [链接示例](https://example.com)\n- `代码片段`你好！支持以下 Markdown 格式：\n- **加粗文本**\n- *斜体文本*\n- [链接示例](https://example.com)\n- `代码片段`你好！支持以下 Markdown 格式：\n- **加粗文本**\n- *斜体文本*\n- [链接示例](https://example.com)\n- `代码片段`'
	}
]); // 聊天列表
watch(
	() => chatList.value.length,
	(e) => {
		if (e) {
			_scrollToBottom();
		}
	}
);
const keyboardListener=function (res){
	console.log("keyboardListener",res)
	console.log('---pageMove---',pageMove.value,res.height,systemInfo?.safeAreaInsets?.bottom);
	pageMove.value=res.height-systemInfo?.safeAreaInsets?.bottom
	console.log('---pageMove---',pageMove.value);
}
/*生命周期*/
onMounted(()=>{
	uni.onKeyboardHeightChange(keyboardListener)
})
onUnmounted(()=>{
	uni.offKeyboardHeightChange(keyboardListener)
})
onReady(() => {
	const scrollViewWrap = uni.createSelectorQuery().select('#scrollViewWrap');
	scrollViewWrap
		.boundingClientRect((res) => {
			if (res) {
				console.log('---scrollViewHeight---',scrollViewHeight.value);
				scrollViewHeight.value = res.height;
				console.log('---scrollViewHeight---',scrollViewHeight.value);
			}
		})
		.exec();
});
onLoad((e) => {
	uni.getSystemInfo({
		success: function (res) {
			console.log(res)
			systemInfo.osName=res.osName
			systemInfo.safeAreaInsets=res.safeAreaInsets
			// if(systemInfo.osName!='ios'){
				
			// }
		}
	});
	if (e.question) {
		console.log("e",e)
		const question = JSON.parse(e.question);
		chatList.value.push(question);
	}
});
nextTick(() => {
	_scrollToBottom();
});
//用户点击分享
onShareAppMessage((e) => {
	let shareInfo = {
		path: '/pages/cardDetail/cardDetail',
		title: '陈功 - 创意设计总监',
		imageUrl: '/static/images/sharePic.png'
	};
	return shareInfo;
});
/*函数*/

function onRichTextClick() {}
//滑倒最底部
function _scrollToBottom() {
	nextTick(() => {
		const scrollBottomMarker = uni.createSelectorQuery().select('#scrollBottomMarker');
		scrollBottomMarker
			.fields(
				{
					rect: true
				},
				(res) => {
					if (res) {
						console.log('scrollToBottom', res);
						scrollTop.value = res.top;
					}
				}
			)
			.exec();
	});
}
//长按
function onLongpress(e) {
	console.log('onLongpress', e);
}
//选择快捷问题
function onSelectPrequestion(item = {}) {
	chatList.value.push(item);
}
// 发送问题
function onSend() {
	const _content=content.value.trim()
	if(_content){
		const obj = {
			content: _content,
			user: 'my'
		};
		chatList.value.push(obj);
		content.value = '';
	}else{
		content.value = '';
	}
}
</script>

<style scoped lang="scss">
.list-wrap {
	// &::-webkit-scrollbar {
	// 	display: none;
	// 	width: 0;
	// 	height: 0;
	// 	color: red;
	// 	background: transparent;
	// }
}
.card-wrap {
	padding-top: 80rpx;
}
.card-main {
	padding-top: 30rpx;
}
.share-box {
	$w: 32rpx;
	width: $w;
	height: $w;
}
.logo-box {
	$w: 112rpx;
	width: $w;
	height: $w;
	position: absolute;
	top: 0;
	transform: translateY(-60%);
	left: 20rpx;
	z-index: 5;
}
.card-box {
	width: 120rpx;
	box-sizing: border-box;
	padding: 0;
}
.btn-box {
	$w: 72rpx;
	width: $w;
	height: $w;
	box-sizing: border-box;
}
.input {
	$w: 88rpx;
	height: $w;
	border-radius: $w;
	background-color: #fff;
	position: relative;
}
.chat-right {
	border-top-right-radius: 0;
}
.chat-right {
	border-top-left-radius: 0;
}
.pageFoot {
	// height:118px;
}
</style>
